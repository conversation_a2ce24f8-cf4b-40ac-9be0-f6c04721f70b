import torch  # 导入torch用于张量操作
import torch.nn as nn  # 导入神经网络模块
import torch.nn.functional as F  # 导入常用函数
import numpy as np  # 导入numpy
from utils import gen_label  # 导入标签生成函数


# 分类损失函数，支持异常和正常样本不同topk策略
# logits: [batch, seq, 1] 或 squeeze后 [batch, seq]，每一帧的异常概率
# label: [batch]，每个视频的标签（0=正常，1=异常）
# seq_len: [batch]，每个视频的有效帧数
# criterion: 损失函数（如BCELoss）
def CLAS2(logits, label, seq_len, criterion):
    logits = logits.squeeze()  # 去掉多余维度 [batch, seq]
    ins_logits = torch.zeros(0).cuda()  # 存储每个样本的topk均值，shape: [batch]
    for i in range(logits.shape[0]):  # 遍历每个视频
        # 对于正常视频，只取最大一帧的分数（假设正常视频中最高分帧最可能为误报）
        if label[i] == 0:  # 正常样本
            # logits[i][:seq_len[i]]: 有效帧的分数
            tmp, _ = torch.topk(logits[i][:seq_len[i]], k=1, largest=True)  # 取最大值
        else:  # 异常样本
            # 对于异常视频，取topk帧（k=帧数//16+1），假设异常只出现在少数帧
            tmp, _ = torch.topk(logits[i][:seq_len[i]], k=int(seq_len[i]//16+1), largest=True)  # 取topk
        tmp = torch.mean(tmp).view(1)  # 求topk分数的均值，shape: [1]
        ins_logits = torch.cat((ins_logits, tmp))  # 拼接到结果

    # ins_logits: [batch]，每个视频的代表分数
    # label: [batch]，视频级标签
    clsloss = criterion(ins_logits, label)  # 计算二分类损失
    return clsloss


# KL散度损失（用于对比学习/分布对齐）
# preds: [N, N]，预测的相似度分布
# label: [N, N]，one-hot标签分布
# criterion: KLDivLoss
# 数学公式：KL(P||Q) = sum(P*log(P/Q))，这里P=softmax(label*10)，Q=softmax(preds)
def KLV_loss(preds, label, criterion):
    preds = F.softmax(preds, dim=1)  # 对每一行做softmax，得到概率分布Q
    preds = torch.log(preds)  # 取对数log(Q)
    if torch.isnan(preds).any():  # 防止数值不稳定
        loss = 0
    else:
        # label*10放大one-hot，softmax后趋近于one-hot
        target = F.softmax(label * 10, dim=1)  # 目标分布P
        loss = criterion(preds, target)  # 计算KL散度
    return loss


# 时间平滑损失（相邻帧分数的平方差和）
# arr: [seq]，一段视频的帧分数
# 数学公式：sum((arr[t+1]-arr[t])^2)
def temporal_smooth(arr):
    arr2 = torch.zeros_like(arr)  # shape同arr
    arr2[:-1] = arr[1:]  # arr2[t]=arr[t+1]
    arr2[-1] = arr[-1]  # 最后一帧补自身
    loss = torch.sum((arr2-arr)**2)  # 所有相邻帧分数的平方差和
    return loss


# 稀疏性损失（分数和，鼓励大部分帧为0）
# arr: [seq]，一段视频的帧分数
# 数学公式：sum(arr)
def temporal_sparsity(arr):
    loss = torch.sum(arr)  # 所有帧分数之和
    loss = torch.sum(arr)
    # loss = torch.mean(torch.norm(arr, dim=0))
    return loss


def Smooth(logits, seq_len, lamda=8e-5):
    smooth_mse = []
    for i in range(logits.shape[0]):
        tmp_logits = logits[i][:seq_len[i]-1]
        sm_mse = temporal_smooth(tmp_logits)
        smooth_mse.append(sm_mse)
    smooth_mse = sum(smooth_mse) / len(smooth_mse)

    return smooth_mse * lamda


def Sparsity(logits, seq_len, lamda=8e-5):
    spar_mse = []
    for i in range(logits.shape[0]):
        tmp_logits = logits[i][:seq_len[i]]
        sp_mse = temporal_sparsity(tmp_logits)
        spar_mse.append(sp_mse)
    spar_mse = sum(spar_mse) / len(spar_mse)

    return spar_mse * lamda


def Smooth_Sparsity(logits, seq_len, lamda=8e-5):
    smooth_mse = []
    spar_mse = []
    for i in range(logits.shape[0]):
        tmp_logits = logits[i][:seq_len[i]]
        sm_mse = temporal_smooth(tmp_logits)
        sp_mse = temporal_sparsity(tmp_logits)
        smooth_mse.append(sm_mse)
        spar_mse.append(sp_mse)
    smooth_mse = sum(smooth_mse) / len(smooth_mse)
    spar_mse = sum(spar_mse) / len(spar_mse)

    return (smooth_mse + spar_mse) * lamda


# 增强的MAL损失函数：边际感知对抗损失，提高异常和正常样本表示之间的区分度
def MAL_loss(features, labels, margin=0.2, temperature=0.1, adaptive_margin=True):
    """
    增强的边际感知对抗损失，增强异常与正常样本之间的区分度

    参数:
    features: [batch_size, feature_dim] 视频特征
    labels: [batch_size] 标签 (0=正常, 1=异常)
    margin: 正负样本间的边际约束
    temperature: 缩放因子，控制分布集中度
    adaptive_margin: 是否使用自适应边际

    返回:
    loss: 标量，MAL损失
    """
    # 特征归一化
    features = F.normalize(features, p=2, dim=1)

    # 区分正常和异常样本
    normal_mask = (labels == 0)
    abnormal_mask = (labels == 1)

    # 如果没有正常样本或异常样本，返回零损失
    if not torch.any(normal_mask) or not torch.any(abnormal_mask):
        return torch.tensor(0.0, device=features.device)

    # 提取正常和异常样本特征
    normal_features = features[normal_mask]
    abnormal_features = features[abnormal_mask]

    # 计算特征相似度矩阵
    sim_matrix = torch.mm(abnormal_features, normal_features.transpose(0, 1)) / temperature

    # 自适应边际计算
    if adaptive_margin:
        # 根据特征分布动态调整边际
        feature_std = torch.std(features, dim=0).mean()
        adaptive_margin_value = margin * (1 + feature_std.item())
    else:
        adaptive_margin_value = margin

    # 计算每个异常样本与所有正常样本的相似度
    pos_sim = sim_matrix.max(dim=1)[0]  # 对每行取最大值
    neg_sim = sim_matrix.mean(dim=1)    # 对每行取平均值

    # 增强的边际感知对抗损失
    # 1. 基础边际损失
    basic_margin_loss = F.relu(adaptive_margin_value - (neg_sim - pos_sim))

    # 2. 类内紧致性损失 - 鼓励同类样本聚集
    normal_center = normal_features.mean(dim=0, keepdim=True)
    abnormal_center = abnormal_features.mean(dim=0, keepdim=True)

    normal_compactness = torch.mean(torch.norm(normal_features - normal_center, dim=1))
    abnormal_compactness = torch.mean(torch.norm(abnormal_features - abnormal_center, dim=1))
    compactness_loss = normal_compactness + abnormal_compactness

    # 3. 类间分离损失 - 鼓励不同类别中心分离
    center_distance = torch.norm(normal_center - abnormal_center)
    separation_loss = F.relu(adaptive_margin_value - center_distance)

    # 综合损失
    total_loss = basic_margin_loss.mean() + 0.1 * compactness_loss + 0.1 * separation_loss

    return total_loss


# 多边际对比损失函数，考虑特征在多个尺度上的区分能力
def MultiMarginContrastive(features, labels, seq_len, margin=0.2, temperature=0.2):
    """
    在多个尺度上应用边际对比损失
    
    参数:
    features: [batch_size, feat_dim, seq_len] 视频特征序列
    labels: [batch_size] 标签 (0=正常, 1=异常)
    seq_len: [batch_size] 每个视频的有效长度
    margin: 边际约束 (降低到0.2)
    temperature: 缩放因子 (增加到0.2以减少梯度幅度)
    """
    # 转置特征以便于处理: [batch_size, seq_len, feat_dim]
    features_t = features.transpose(1, 2)
    batch_size = features_t.shape[0]
    
    # 全局特征 (平均池化)
    global_feats = []
    for i in range(batch_size):
        # 只考虑有效长度
        valid_feat = features_t[i, :seq_len[i], :]
        if valid_feat.shape[0] > 0:
            global_feats.append(torch.mean(valid_feat, dim=0))
        else:
            # 处理长度为0的情况
            global_feats.append(torch.zeros(features_t.shape[2], device=features_t.device))
    
    global_feats = torch.stack(global_feats)
    
    # 全局特征的MAL损失
    global_loss = MAL_loss(global_feats, labels, margin, temperature)
    
    # 局部特征 (按时间分段)
    segment_sizes = [2, 4]  # 将序列分成2段和4段
    segment_losses = []
    
    for segments in segment_sizes:
        segment_feats = []
        segment_labels = []
        
        for i in range(batch_size):
            if seq_len[i] < segments:
                continue  # 序列太短，跳过
            
            # 计算每段长度
            seg_length = seq_len[i] // segments
            
            for j in range(segments):
                start_idx = j * seg_length
                end_idx = (j+1) * seg_length if j < segments - 1 else seq_len[i]
                
                # 确保段长度大于0
                if end_idx > start_idx:
                    seg_feat = torch.mean(features_t[i, start_idx:end_idx, :], dim=0)
                    segment_feats.append(seg_feat)
                    segment_labels.append(labels[i])
        
        if len(segment_feats) > 0:
            segment_feats = torch.stack(segment_feats)
            segment_labels = torch.stack(segment_labels)
            segment_loss = MAL_loss(segment_feats, segment_labels, margin, temperature)
            segment_losses.append(segment_loss)
    
    # 结合全局和局部损失
    if len(segment_losses) > 0:
        total_loss = global_loss + torch.mean(torch.stack(segment_losses))
    else:
        total_loss = global_loss
    
    return total_loss