from torch.utils.data import DataLoader
import torch.optim as optim
import torch
import time
import numpy as np
import random
from configs import build_config
from utils import setup_seed
from log import get_logger

# 添加SWA优化器
try:
    from torch.optim.swa_utils import AveragedModel, SWALR
    has_swa = True
except ImportError:
    has_swa = False
    print("Warning: torch.optim.swa_utils not available, please upgrade PyTorch to use SWA")

from model import XModel
from dataset import *

from train import train_func
from test import test_func
from infer import infer_func
import argparse
import copy
import math

import os
# os.environ['CUDA_VISIBLE_DEVICES'] = '3'
os.environ['CUDA_VISIBLE_DEVICES'] = '0'


def load_checkpoint(model, ckpt_path, logger):
    """
    加载预训练模型权重到当前模型。
    遍历权重字典，将权重加载到模型对应参数。
    支持DataParallel保存的权重名处理。
    """
    if os.path.isfile(ckpt_path):
        logger.info('loading pretrained checkpoint from {}.'.format(ckpt_path))
        weight_dict = torch.load(ckpt_path)
        model_dict = model.state_dict()
        for name, param in weight_dict.items():
            if 'module' in name:
                name = '.'.join(name.split('.')[1:])
            if name in model_dict:
                if param.size() == model_dict[name].size():
                    model_dict[name].copy_(param)
                else:
                    logger.info('{} size mismatch: load {} given {}'.format(
                        name, param.size(), model_dict[name].size()))
            else:
                logger.info('{} not found in model dict.'.format(name))
    else:
        logger.info('Not found pretrained checkpoint file.')


def train(model, train_loader, test_loader, gt, logger):
    """
    训练主循环，包括损失函数、优化器、学习率调度器的定义。
    每轮训练后在测试集上评估，保存最佳模型。
    """
    if not os.path.exists(cfg.save_dir):
        os.makedirs(cfg.save_dir)

    criterion = torch.nn.BCELoss()
    criterion2 = torch.nn.KLDivLoss(reduction='batchmean')
    
    # 优化的参数分组和权重衰减
    weight_decay = 5e-5  # 改回较小的值

    # 分组参数以实现差异化学习率
    param_groups = []

    # TCA模块参数
    tca_params = [p for n, p in model.named_parameters() if 'self_attn' in n and p.requires_grad]
    if tca_params:
        param_groups.append({'params': tca_params, 'lr': cfg.lr, 'weight_decay': weight_decay})

    # VMRNN模块参数 - 稍低学习率
    vmrnn_params = [p for n, p in model.named_parameters() if 'vmrnn' in n and p.requires_grad]
    if vmrnn_params:
        param_groups.append({'params': vmrnn_params, 'lr': cfg.lr * 0.8, 'weight_decay': weight_decay})

    # 特征放大器参数 - 稍高学习率
    amplifier_params = [p for n, p in model.named_parameters() if 'feature_amplifier' in n and p.requires_grad]
    if amplifier_params:
        param_groups.append({'params': amplifier_params, 'lr': cfg.lr * 1.2, 'weight_decay': weight_decay})

    # 其他参数
    other_params = [p for n, p in model.named_parameters()
                   if not any(x in n for x in ['self_attn', 'vmrnn', 'feature_amplifier']) and p.requires_grad]
    if other_params:
        param_groups.append({'params': other_params, 'lr': cfg.lr, 'weight_decay': weight_decay})

    # 如果没有分组参数，使用默认配置
    if not param_groups:
        param_groups = [{'params': model.parameters(), 'lr': cfg.lr, 'weight_decay': weight_decay}]

    base_optimizer = optim.AdamW(param_groups)  # 使用AdamW替代Adam
    logger.info(f'Using weight decay (L2 regularization): {weight_decay}')
    logger.info(f'Parameter groups: {len(param_groups)}')
    
    # 使用更温和的学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        base_optimizer,
        mode='max',
        factor=0.8,   # 更温和的学习率衰减
        patience=10,  # 修改耐心值为10轮
        verbose=True,
        min_lr=1e-6
    )

    # 早停设置 - 减少早停耐心值以配合学习率调度
    patience = 15
    patience_counter = 0
    
    # 设置SWA相关组件 - 回到较后期开始SWA
    swa_start = 15  # 在第15轮开始SWA
    logger.info(f'SWA will start at epoch {swa_start}')
    
    if has_swa:
        try:
            device = next(model.parameters()).device
            swa_model = AveragedModel(model)
            swa_model = swa_model.to(device)
            
            # 使用更温和的SWA学习率
            swa_scheduler = SWALR(
                base_optimizer, 
                swa_lr=cfg.lr * 0.2,  # 不要太小的学习率
                anneal_epochs=5,
                anneal_strategy="cos"  # 回到余弦退火
            )
            logger.info('Successfully initialized SWA model and scheduler')
        except Exception as e:
            logger.error(f'Error initializing SWA: {str(e)}')
            swa_model = None
            swa_scheduler = None
    else:
        swa_model = None
        swa_scheduler = None

    optimizer = base_optimizer
    
    logger.info(f'Model:{model}\n')
    logger.info(f'Optimizer:{optimizer}\n')

    initial_auc, n_far = test_func(test_loader, model, gt, cfg.dataset)
    logger.info(f'Random initialize {cfg.metrics}:{initial_auc:.4f} FAR:{n_far:.5f}')

    best_model_wts = copy.deepcopy(model.state_dict())
    best_auc = 0.0
    auc_far = 0.0
    
    st = time.time()
    for epoch in range(cfg.max_epoch):
        # 训练阶段 - 传入epoch参数用于渐进式训练
        loss1, loss2 = train_func(train_loader, model, optimizer, criterion, criterion2, cfg.lamda, epoch)

        # 协同性监控 - 检查模块间的协同状态
        if hasattr(model.self_attention, 'self_attn') and hasattr(model.self_attention, 'feature_amplifier'):
            if hasattr(model.self_attention.self_attn, 'alpha') and hasattr(model.self_attention.feature_amplifier, 'alpha_enhance'):
                tca_alpha = torch.sigmoid(model.self_attention.self_attn.alpha).item()
                amp_alpha = torch.sigmoid(model.self_attention.feature_amplifier.alpha_enhance).item()
                alpha_diff = abs(tca_alpha - amp_alpha)
                if epoch % 10 == 0:  # 每10个epoch记录一次
                    logger.info(f'Module sync - TCA alpha: {tca_alpha:.4f}, Amplifier alpha: {amp_alpha:.4f}, diff: {alpha_diff:.4f}')

        # 使用SWA
        if has_swa and swa_model is not None and epoch >= swa_start:
            try:
                swa_model.update_parameters(model)
                if swa_scheduler is not None:
                    swa_scheduler.step()
            except Exception as e:
                logger.error(f'Error in SWA update at epoch {epoch + 1}: {str(e)}')

        # 评估阶段
        auc, far = test_func(test_loader, model, gt, cfg.dataset)

        # 更新学习率
        scheduler.step(auc)

        # 打印当前epoch的信息
        curr_lr = optimizer.param_groups[0]['lr']
        logger.info(f'[Epoch:{epoch+1}/{cfg.max_epoch}]: lr:{curr_lr:.6f} loss1:{loss1:.4f} loss2:{loss2:.4f} | AUC:{auc:.4f} FAR:{far:.5f}')
        
        # 记录最佳模型
        if auc > best_auc:
            best_auc = auc
            auc_far = far
            best_model_wts = copy.deepcopy(model.state_dict())
            logger.info(f'New best model found at epoch {epoch+1} with AUC: {auc:.4f}')
            patience_counter = 0
        else:
            patience_counter += 1
            logger.info(f'No improvement for {patience_counter} epochs (patience: {patience})')
            
        # 早停检查
        if patience_counter >= patience:
            logger.info(f'Early stopping triggered after {epoch+1} epochs')
            break

    # 如果使用了SWA，在训练结束时进行最终更新和评估
    if has_swa and swa_model is not None and epoch >= swa_start:
        try:
            logger.info('Updating SWA BatchNorm statistics...')
            torch.optim.swa_utils.update_bn(train_loader, swa_model, device=device)
            
            swa_auc, swa_far = test_func(test_loader, swa_model, gt, cfg.dataset)
            logger.info(f'SWA model performance: {cfg.metrics}:{swa_auc:.4f} FAR:{swa_far:.5f}')
            
            if swa_auc > best_auc:
                try:
                    swa_state = swa_model.state_dict()
                    
                    # 清理状态字典
                    cleaned_state_dict = {}
                    for k, v in swa_state.items():
                        # 跳过SWA特有的键
                        if k in ['n_averaged']:
                            continue
                        # 移除module前缀
                        if k.startswith('module.'):
                            cleaned_state_dict[k[7:]] = v
                        else:
                            cleaned_state_dict[k] = v
                    
                    best_auc = swa_auc
                    auc_far = swa_far
                    best_model_wts = cleaned_state_dict
                    logger.info('SWA weights selected as best model')
                except Exception as e:
                    logger.error(f'Error processing SWA state dict: {str(e)}')
        except Exception as e:
            logger.error(f'Error in final SWA processing: {str(e)}')

    time_elapsed = time.time() - st
    try:
        # 确保状态字典干净
        if isinstance(best_model_wts, dict):
            best_model_wts.pop('n_averaged', None)
            for k in list(best_model_wts.keys()):
                if k.startswith('module.'):
                    best_model_wts[k[7:]] = best_model_wts.pop(k)
        
        model.load_state_dict(best_model_wts)
        save_path = cfg.save_dir + cfg.model_name + '_' + str(round(best_auc, 4)).split('.')[1] + '.pkl'
        torch.save(best_model_wts, save_path)
        logger.info(f'Model saved to {save_path}')
    except Exception as e:
        logger.error(f'Error saving model: {str(e)}')
    
    logger.info(f'Training completes in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s | best {cfg.metrics}:{best_auc:.4f} FAR:{auc_far:.5f}\n')


def main(cfg):
    """
    主流程，包含日志初始化、随机种子设置、数据集加载、模型构建、训练/推理分支。
    """
    # 更新配置：应用命令行参数的值
    if args.use_vmrnn is not None:
        cfg.use_vmrnn = args.use_vmrnn
    if args.rnn_depth is not None:
        cfg.rnn_depth = args.rnn_depth
    if args.d_state is not None:
        cfg.d_state = args.d_state
    if args.batch_size is not None:
        cfg.train_bs = args.batch_size
        
    logger = get_logger(cfg.logs_dir)
    setup_seed(cfg.seed)
    logger.info('Config:{}'.format(cfg.__dict__))

    # 根据数据集类型选择不同的数据集类
    if cfg.dataset == 'ucf-crime':
        train_data = UCFDataset(cfg, test_mode=False)
        test_data = UCFDataset(cfg, test_mode=True)
    elif cfg.dataset == 'xd-violence':
        train_data = XDataset(cfg, test_mode=False)
        test_data = XDataset(cfg, test_mode=True)
    elif cfg.dataset == 'shanghaiTech':
        train_data = SHDataset(cfg, test_mode=False)
        test_data = SHDataset(cfg, test_mode=True)
    else:
        raise RuntimeError("Do not support this dataset!")

    # 构建训练和测试数据加载器
    train_loader = DataLoader(train_data, batch_size=cfg.train_bs, shuffle=True,
                              num_workers=cfg.workers, pin_memory=True)

    test_loader = DataLoader(test_data, batch_size=cfg.test_bs, shuffle=False,
                             num_workers=cfg.workers, pin_memory=True)

    # 构建模型
    model = XModel(cfg)
    gt = np.load(cfg.gt)
    device = torch.device("cuda")
    model = model.to(device)

    # 统计模型参数量
    param = sum(p.numel() for p in model.parameters())
    logger.info('total params:{:.4f}M'.format(param / (1000 ** 2)))
    
    # 输出模型配置信息
    model_type = "Standard TCA"
    if cfg.use_vmrnn:
        model_type = f"VMRNN (depth={cfg.rnn_depth}, d_state={cfg.d_state})"
    logger.info(f'Using model type: {model_type}')

    # 根据模式选择训练或推理
    if args.mode == 'train':
        logger.info('Training Mode')
        train(model, train_loader, test_loader, gt, logger)

    elif args.mode == 'infer':
        logger.info('Test Mode')
        if cfg.ckpt_path is not None:
            load_checkpoint(model, cfg.ckpt_path, logger)
        else:
            logger.info('infer from random initialization')
        infer_func(model, test_loader, gt, logger, cfg)

    else:
        raise RuntimeError('Invalid status!')


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='WeaklySupAnoDet')
    parser.add_argument('--dataset', default='ucf', help='anomaly video dataset')
    parser.add_argument('--mode', default='train', help='model status: (train or infer)')
    
    # 添加VMRNN相关参数
    parser.add_argument('--use_vmrnn', type=lambda x: x.lower() == 'true', default=None,
                        help='是否使用Vision Mamba RNN (true/false)')
    parser.add_argument('--rnn_depth', type=int, default=None,
                        help='VMRNN的层数')
    parser.add_argument('--d_state', type=int, default=None,
                        help='状态空间维度')

    # 添加特征放大器相关参数
    parser.add_argument('--use_amplifier', type=lambda x: x.lower() == 'true', default=None,
                        help='是否使用特征放大器 (true/false)')
    parser.add_argument('--amplify_ratio', type=float, default=None,
                        help='特征放大倍数 (1.0-3.0)')

    # 添加batch_size参数
    parser.add_argument('--batch_size', type=int, default=None,
                        help='训练的batch size')

    args = parser.parse_args()
    cfg = build_config(args.dataset)

    # 应用命令行参数覆盖配置
    if args.use_vmrnn is not None:
        cfg.use_vmrnn = args.use_vmrnn
    if args.rnn_depth is not None:
        cfg.rnn_depth = args.rnn_depth
    if args.d_state is not None:
        cfg.d_state = args.d_state
    if args.use_amplifier is not None:
        cfg.use_amplifier = args.use_amplifier
    if args.amplify_ratio is not None:
        cfg.amplify_ratio = args.amplify_ratio
    if args.batch_size is not None:
        cfg.train_bs = args.batch_size
    main(cfg)
