import torch  # 导入torch用于张量操作
import torch.nn as nn  # 导入神经网络模块
from torch import FloatTensor  # 导入FloatTensor类型
from torch.nn.parameter import Parameter  # 导入Parameter类型
from scipy.spatial.distance import pdist, squareform  # 导入距离计算函数
import torch.nn.functional as F  # 导入常用函数
import numpy as np  # 导入numpy
import math  # 导入数学库


class DistanceAdj(nn.Module):
    def __init__(self, sigma, bias):
        super(DistanceAdj, self).__init__()
        # self.sigma = sigma
        # self.bias = bias
        self.w = nn.Parameter(torch.FloatTensor(1))  # 可学习参数w
        self.b = nn.Parameter(torch.FloatTensor(1))  # 可学习参数b
        self.w.data.fill_(sigma)  # 初始化w
        self.b.data.fill_(bias)  # 初始化b

    def forward(self, batch_size, seq_len):
        arith = np.arange(seq_len).reshape(-1, 1)  # 生成序列索引
        dist = pdist(arith, metric='cityblock').astype(np.float32)  # 计算曼哈顿距离
        dist = torch.from_numpy(squareform(dist)).cuda()  # 转为方阵并搬到GPU
        # dist = torch.exp(-self.sigma * dist ** 2)
        dist = torch.exp(-torch.abs(self.w * dist ** 2 - self.b))  # 距离邻接权重
        dist = torch.unsqueeze(dist, 0).repeat(batch_size, 1, 1)  # 扩展到batch

        return dist  # [batch, seq_len, seq_len]


class TCA(nn.Module):
    def __init__(self, d_model, dim_k, dim_v, n_heads, norm=None):
        super(TCA, self).__init__()
        # 确保维度可以被头数整除
        assert dim_k % n_heads == 0, f"dim_k ({dim_k}) must be divisible by n_heads ({n_heads})"
        assert dim_v % n_heads == 0, f"dim_v ({dim_v}) must be divisible by n_heads ({n_heads})"

        self.d_model = d_model
        self.dim_v = dim_v  # value通道数
        self.dim_k = dim_k  # key/query通道数
        self.n_heads = n_heads  # 注意力头数
        self.norm = norm  # 是否归一化
        self.head_dim_k = dim_k // n_heads
        self.head_dim_v = dim_v // n_heads

        # 使用融合QKV投影提高效率
        self.qkv = nn.Linear(d_model, dim_k + dim_k + dim_v, bias=False)
        self.o = nn.Linear(dim_v, d_model)  # 输出投影

        self.norm_fact = 1 / math.sqrt(self.head_dim_k)  # 缩放因子
        self.alpha = nn.Parameter(torch.tensor(0.))  # 可学习融合系数

        # 增强的全局-局部平衡机制
        self.beta = nn.Parameter(torch.tensor(0.5))  # 额外的平衡参数
        self.dropout = nn.Dropout(0.1)  # 添加dropout

    def forward(self, x, mask, adj=None):
        batch_size, seq_len, d_model = x.shape

        # 高效的QKV计算
        qkv = self.qkv(x)  # [batch, seq, dim_k + dim_k + dim_v]
        q, k, v = qkv.chunk(3, dim=-1)  # 分割为Q, K, V

        # 重塑为多头格式 [batch, n_heads, seq, head_dim]
        q = q.view(batch_size, seq_len, self.n_heads, self.head_dim_k).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.n_heads, self.head_dim_k).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.n_heads, self.head_dim_v).transpose(1, 2)

        # 计算注意力分数
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) * self.norm_fact  # [batch, n_heads, seq, seq]

        # 添加邻接矩阵（如果提供）
        if adj is not None:
            # 确保邻接矩阵维度匹配
            if adj.dim() == 3:  # [batch, seq, seq]
                adj = adj.unsqueeze(1)  # [batch, 1, seq, seq]
            attn_scores = attn_scores + adj

        # 全局注意力
        g_map = F.softmax(attn_scores, dim=-1)
        g_map = self.dropout(g_map)
        glb = torch.matmul(g_map, v)  # [batch, n_heads, seq, head_dim_v]

        # 局部注意力
        l_map = attn_scores.clone()
        if mask is not None:
            # 确保mask维度匹配
            if mask.dim() == 4 and mask.shape[0] == self.n_heads:  # [n_heads, batch, seq, seq]
                mask = mask.permute(1, 0, 2, 3)  # [batch, n_heads, seq, seq]
            elif mask.dim() == 3:  # [batch, seq, seq]
                mask = mask.unsqueeze(1).expand(-1, self.n_heads, -1, -1)
            l_map = l_map.masked_fill(mask == 0, -1e9)

        l_map = F.softmax(l_map, dim=-1)
        l_map = self.dropout(l_map)
        lcl = torch.matmul(l_map, v)  # [batch, n_heads, seq, head_dim_v]

        # 重塑回原始格式
        glb = glb.transpose(1, 2).contiguous().view(batch_size, seq_len, self.dim_v)
        lcl = lcl.transpose(1, 2).contiguous().view(batch_size, seq_len, self.dim_v)

        # 增强的自适应融合
        alpha = torch.sigmoid(self.alpha)
        beta = torch.sigmoid(self.beta)

        # 动态权重调整：根据特征差异调整融合比例
        feature_diff = torch.norm(glb - lcl, dim=-1, keepdim=True)  # [batch, seq, 1]
        adaptive_weight = torch.sigmoid(feature_diff * beta)

        tmp = alpha * adaptive_weight * glb + (1 - alpha * adaptive_weight) * lcl

        # 改进的归一化
        if self.norm:
            # 更稳定的power norm
            eps = 1e-8
            tmp_pos = F.relu(tmp)
            tmp_neg = F.relu(-tmp)
            tmp = torch.sqrt(tmp_pos + eps) - torch.sqrt(tmp_neg + eps)
            tmp = F.normalize(tmp, p=2, dim=-1, eps=eps)

        # 输出投影
        output = self.o(tmp)
        return output  # [batch, seq, d_model]
