
import torch  # 导入torch用于张量操作
from modules import *  # 导入自定义模块
import torch.nn.init as torch_init  # 导入权重初始化方法
import numpy as np  # 导入numpy


# 权重初始化函数，对Conv和Linear层使用xavier初始化
def weight_init(m):
    classname = m.__class__.__name__  # 获取类名
    if classname.find('Conv') != -1 or classname.find('Linear') != -1:  # 卷积或全连接层
        torch_init.xavier_uniform_(m.weight)  # xavier均匀初始化权重
        # m.bias.data.fill_(0.1)  # 可选：初始化偏置


# 主模型类，包含自注意力和分类器
class XModel(nn.Module):
    def __init__(self, cfg):
        super(XModel, self).__init__()
        self.t = cfg.t_step  # 分类器卷积核长度

        # 获取VMRNN相关配置
        use_vmrnn = getattr(cfg, 'use_vmrnn', False)  # 默认不使用VMRNN
        rnn_depth = getattr(cfg, 'rnn_depth', 1)  # VMRNN层数
        d_state = getattr(cfg, 'd_state', 16)  # VMRNN状态维度

        # 获取特征放大器相关配置
        use_amplifier = getattr(cfg, 'use_amplifier', True)  # 默认使用特征放大器
        amplify_ratio = getattr(cfg, 'amplify_ratio', 1.0)  # 特征放大倍数

        # 维度检查和自动调整
        if getattr(cfg, 'dimension_check', True):
            # 确保hid_dim能被head_num整除
            if cfg.hid_dim % cfg.head_num != 0:
                cfg.hid_dim = ((cfg.hid_dim // cfg.head_num) + 1) * cfg.head_num
                print(f"Auto-adjusted hid_dim to {cfg.hid_dim} for head compatibility")

            # 确保out_dim能被head_num整除
            if cfg.out_dim % cfg.head_num != 0:
                cfg.out_dim = ((cfg.out_dim // cfg.head_num) + 1) * cfg.head_num
                print(f"Auto-adjusted out_dim to {cfg.out_dim} for head compatibility")

        self.self_attention = XEncoder(
            d_model=cfg.feat_dim,  # 输入特征维度（通道数）
            hid_dim=cfg.hid_dim,  # 注意力隐藏层维度
            out_dim=cfg.out_dim,  # 输出特征维度
            n_heads=cfg.head_num,  # 注意力头数
            win_size=cfg.win_size,  # 局部窗口大小
            dropout=cfg.dropout,  # dropout概率
            gamma=cfg.gamma,  # 距离邻接参数
            bias=cfg.bias,  # 距离邻接偏置
            norm=cfg.norm,  # 是否归一化
            use_vmrnn=use_vmrnn,  # 是否使用VMRNN
            rnn_depth=rnn_depth,  # VMRNN层数
            d_state=d_state,  # VMRNN状态维度
            use_amplifier=use_amplifier,  # 是否使用特征放大器
            amplify_ratio=amplify_ratio,  # 特征放大倍数
        )

        # 自适应分类器 - 根据输出维度调整
        self.classifier = nn.Conv1d(cfg.out_dim, 1, self.t, padding=0)  # 1D卷积分类器
        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / cfg.temp))  # logit缩放参数

        # 维度匹配层（如果需要）
        self.dim_matcher = nn.Identity()  # 默认恒等映射

        self.apply(weight_init)  # 应用权重初始化

        # 用于处理VMRNN的状态维护
        self.use_vmrnn = use_vmrnn

        # 协同优化标志
        self.enable_cross_module_sync = getattr(cfg, 'enable_cross_module_sync', True)

    def forward(self, x, seq_len):
        x_e, x_v = self.self_attention(x, seq_len)  # x_e: [batch, out_dim, seq]，x_v: [batch, d_model//2, seq]
        logits = F.pad(x_e, (self.t - 1, 0))  # 在序列前pad t-1个0，保证卷积后长度一致
        logits = self.classifier(logits)  # [batch, 1, seq]，卷积分类

        logits = logits.permute(0, 2, 1)  # [batch, seq, 1]
        logits = torch.sigmoid(logits)  # 概率化输出

        return logits, x_v  # 返回预测分数和特征
    
    def reset_states(self):
        """重置VMRNN的状态（如果使用）"""
        if self.use_vmrnn:
            self.self_attention.reset_states()
