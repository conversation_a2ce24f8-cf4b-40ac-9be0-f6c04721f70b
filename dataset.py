import torch.utils.data as data  # 导入PyTorch数据集基类
from utils import process_feat  # 导入特征处理函数
import numpy as np  # 导入numpy用于数值计算
import os  # 导入os用于文件路径操作


class UCFDataset(data.Dataset):
    def __init__(self, cfg, transform=None, test_mode=False):
        self.feat_prefix = cfg.feat_prefix  # 特征文件前缀路径
        if test_mode:
            self.list_file = cfg.test_list  # 测试集列表
        else:
            self.list_file = cfg.train_list  # 训练集列表
        self.max_seqlen = cfg.max_seqlen  # 最大序列长度
        self.tranform = transform  # 可选的特征变换
        self.test_mode = test_mode  # 是否为测试模式
        self.normal_flag = 'Normal'  # 正常类别标志
        self.abnormal_dict = {'Normal':0,'Abuse':1, 'Arrest':2, 'Arson':3, 'Assault':4,
                              'Burglary':5, 'Explosion':6, 'Fighting':7,'RoadAccidents':8,
                              'Robbery':9, 'Shooting':10, 'Shoplifting':11, 'Stealing':12, 'Vandalism':13}  # 类别映射
        self.t_features = np.array(np.load(cfg.token_feat))  # token特征，形状[num_class, 512]

        # 初始化特征缓存
        self._feature_cache = {}

        self._parse_list()  # 解析视频列表

    def _parse_list(self):
        self.list = list(open(self.list_file))  # 读取视频文件列表

    def __getitem__(self, index):
        """
        优化的数据获取方法
        增加缓存和内存管理
        """
        feat_path = os.path.join(self.feat_prefix, self.list[index].strip('\n'))  # 特征文件路径
        video_idx = self.list[index].strip('\n').split('/')[-1].split('_')[0]  # 视频类别名

        # 标签处理
        if self.normal_flag in self.list[index]:  # 正常视频
            video_ano = video_idx
            ano_idx = self.abnormal_dict[video_ano]
            label = 0.0
        else:  # 异常视频
            video_ano = video_idx[:-3]
            ano_idx = self.abnormal_dict[video_ano]
            label = 1.0

        # 优化的特征加载
        try:
            # 检查缓存
            if hasattr(self, '_feature_cache') and feat_path in self._feature_cache:
                v_feat = self._feature_cache[feat_path].copy()
            else:
                v_feat = np.load(feat_path).astype(np.float32)  # 视频特征，形状[帧数, feat_dim]

                # 缓存小文件
                if hasattr(self, '_feature_cache') and v_feat.nbytes < 1024 * 1024:  # 小于1MB
                    if len(self._feature_cache) < 100:  # 限制缓存大小
                        self._feature_cache[feat_path] = v_feat.copy()
        except Exception as e:
            print(f"Error loading {feat_path}: {e}")
            # 创建默认特征
            v_feat = np.zeros((self.max_seqlen, 1024), dtype=np.float32)

        # Token特征处理
        fg_feat = self.t_features[ano_idx, :].astype(np.float16)  # 前景token特征[512]
        bg_feat = self.t_features[0, :].astype(np.float16)  # 背景token特征[512]

        # 高效的特征组合
        t_feat = np.stack([bg_feat, fg_feat], axis=0)  # [2, 512] - 使用stack替代concatenate

        # 可选的特征变换
        if self.tranform is not None:
            v_feat = self.tranform(v_feat)
            t_feat = self.tranform(t_feat)

        if self.test_mode:
            return v_feat, label  # 测试返回特征和标签
        else:
            v_feat = process_feat(v_feat, self.max_seqlen, is_random=False)  # 处理特征长度
            return v_feat, t_feat, label, ano_idx  # 训练返回特征、token、标签、类别索引

    def __len__(self):
        return len(self.list)  # 返回样本数


class XDataset(data.Dataset):
    def __init__(self, cfg, transform=None, test_mode=False):
        self.feat_prefix = cfg.feat_prefix  # 特征文件前缀路径
        if test_mode:
            self.list_file = cfg.test_list  # 测试集列表
        else:
            self.list_file = cfg.train_list  # 训练集列表

        self.max_seqlen = cfg.max_seqlen  # 最大序列长度
        self.tranform = transform  # 可选的特征变换
        self.test_mode = test_mode  # 是否为测试模式
        self.t_features = np.load(cfg.token_feat)  # token特征，形状[num_class, 512]
        self.normal_flag = '_label_A'  # 正常类别标志
        self.abnormal_dict = {'A': 0, 'B5': 1, 'B6': 2, 'G': 3, 'B1': 4, 'B4': 5, 'B2': 6}  # 类别映射
        self._parse_list()  # 解析视频列表

    def _parse_list(self):
        self.list = list(open(self.list_file))  # 读取视频文件列表

    def __getitem__(self, index):
        if self.normal_flag in self.list[index]:  # 正常视频
            label = 0.0
        else:  # 异常视频
            label = 1.0

        feat_path = os.path.join(self.feat_prefix, self.list[index].strip('\n'))  # 特征文件路径
        v_feat = np.array(np.load(feat_path), dtype=np.float32)  # 视频特征，形状[帧数, feat_dim]
        tokens = self.list[index].strip('\n').split('_label_')[-1].split('__')[0].split('-')  # 类别token
        idx = self.abnormal_dict[tokens[0]]  # 类别索引
        fg_feat = self.t_features[idx, :].reshape(1, 512)  # 前景token特征[1, 512]
        bg_feat = self.t_features[0, :].reshape(1, 512)  # 背景token特征[1, 512]
        t_feat = np.concatenate((bg_feat, fg_feat), axis=0)  # [2, 512]
        if self.tranform is not None:
            v_feat = self.tranform(v_feat)  # 可选特征变换
            t_feat = self.tranform(t_feat)
        if self.test_mode:
            return v_feat, self.list[index]  # 测试返回特征和文件名
        else:
            v_feat = process_feat(v_feat, self.max_seqlen, is_random=False)  # 处理特征长度
            return v_feat, t_feat, label, idx  # 训练返回特征、token、标签、类别索引

    def __len__(self):
        return len(self.list)  # 返回样本数


class SHDataset(data.Dataset):
    def __init__(self, cfg, transform=None, test_mode=False):
        self.feat_prefix = cfg.feat_prefix  # 特征文件前缀路径
        if test_mode:
            self.list_file = cfg.test_list  # 测试集列表
        else:
            self.list_file = cfg.train_list  # 训练集列表

        self.max_seqlen = cfg.max_seqlen  # 最大序列长度
        self.tranform = transform  # 可选的特征变换
        self.test_mode = test_mode  # 是否为测试模式
        self.abn_file = cfg.abn_label  # 异常标签文件
        self.cls_dict = {'cycling': 1, 'chasing': 2, 'handcart': 3, 'fighting': 4,'skateboarding': 5,
                         'vehicle': 6, 'running': 7, 'jumping': 8, 'wandering': 9, 'lifting': 10,
                         'robbery': 11, 'climbing_over': 12, 'throwing': 13}  # 类别映射
        self.tokens = np.array(np.load(cfg.token_feat))  # token特征，形状[num_class, 512]
        self._parse_list()  # 解析视频列表

    def _parse_list(self):
        self.list = list(open(self.list_file))  # 读取视频文件列表
        self.abn_dict = {}  # 视频名到异常动作的映射
        self.abn_list = []  # 异常视频名列表

        with open(self.abn_file, 'r') as f:
            f = f.readlines()
            for line in f:
                name = line.strip('\n').split(' ')[0]
                label = line.strip('\n').split(' ')[1]
                action = label.split(',')
                self.abn_dict[name] = action
                self.abn_list.append(name)

    def __getitem__(self, index):
        video_name = self.list[index].strip('\n').split(' ')[0].split('/')[-1][:-6]  # 视频名
        video_path = os.path.join(self.feat_prefix, self.list[index].strip('\n').split(' ')[0])  # 特征文件路径
        v_feat = np.array(np.load(video_path), dtype=np.float32)  # 视频特征，形状[帧数, feat_dim]

        if self.tranform is not None:
            v_feat = self.tranform(v_feat)  # 可选特征变换

        if not self.test_mode:
            if video_name in self.abn_list:  # 异常视频
                cls = self.abn_dict[video_name]
                abn_idx = [self.cls_dict[i] for i in cls]
            else:  # 正常视频
                abn_idx = [0]
            fg_feat = np.array(self.tokens[abn_idx, :]).reshape(-1, 512)  # 前景token特征[n, 512]
            fg_feat = np.mean(fg_feat, axis=0).reshape(1, 512)  # 多个动作取均值[1, 512]
            bg_feat = np.array(self.tokens[0, :]).reshape(1, 512)  # 背景token特征[1, 512]
            t_feat = np.concatenate((bg_feat, fg_feat), axis=0)  # [2, 512]

            label = float(self.list[index].strip('\n').split(' ')[1])  # 标签
            v_feat = process_feat(v_feat, self.max_seqlen, is_random=False)  # 处理特征长度
            return v_feat, t_feat, label, abn_idx[0]  # 返回特征、token、标签、类别索引

        else:
            return v_feat, video_name  # 测试返回特征和视频名

    def __len__(self):
        return len(self.list)  # 返回样本数
