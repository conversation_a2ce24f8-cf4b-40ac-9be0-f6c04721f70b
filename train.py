import torch
from loss import *
from utils import *


# 训练流程函数，包含主损失和辅助损失
# dataloader: 训练数据加载器
# model: 神经网络模型
# optimizer: 优化器
# criterion: 主损失函数（如BCELoss）
# criterion2: 辅助损失函数（如KLDivLoss）
# lamda: 辅助损失权重
def train_func(dataloader, model, optimizer, criterion, criterion2, lamda=0, epoch=0):
    t_loss = []  # 分类损失列表，记录每个batch的主损失
    s_loss = []  # KL损失列表，记录每个batch的辅助损失
    m_loss = []  # MAL损失列表，记录每个batch的MAL损失

    with torch.set_grad_enabled(True):  # 启用梯度计算
        model.train()  # 设置模型为训练模式
        for i, (v_input, t_input, label, multi_label) in enumerate(dataloader):
            # 重置VMRNN状态（如果模型使用VMRNN）
            if hasattr(model, 'reset_states'):
                model.reset_states()
                
            # v_input: [batch, seq_len, feat_dim]，视频特征输入
            # t_input: [batch, 2, 512]，文本token特征输入
            # label: [batch]，0/1标签，表示异常/正常
            # multi_label: [batch]，多标签，辅助任务
            seq_len = torch.sum(torch.max(torch.abs(v_input), dim=2)[0] > 0, 1)  # 计算每个样本的有效帧数，形状[batch]
            v_input = v_input[:, :torch.max(seq_len), :]  # 截断所有样本到最大有效帧数，形状[batch, max_seq, feat_dim]
            v_input = v_input.float().cuda(non_blocking=True)  # 转为float并搬到GPU
            t_input = t_input.float().cuda(non_blocking=True)  # token特征搬到GPU
            label = label.float().cuda(non_blocking=True)  # 标签搬到GPU
            multi_label = multi_label.cuda(non_blocking=True)  # 多标签搬到GPU

            logits, v_feat = model(v_input, seq_len)  # 前向传播，logits: [batch, seq, 1]，v_feat: [batch, seq, out_dim]
            
            # Prompt-Enhanced Learning（提示增强学习）
            logit_scale = model.logit_scale.exp()  # 获取logit缩放因子
            video_feat, token_feat, video_labels = get_cas(v_feat, t_input, logits, multi_label)  # 计算视频和token特征及标签
            v2t_logits, v2v_logits = create_logits(video_feat, token_feat, logit_scale)  # 计算视频-文本和视频-视频的logits
            ground_truth = torch.tensor(gen_label(video_labels), dtype=v_feat.dtype).cuda()  # 生成一组one-hot标签
            
            loss2 = KLV_loss(v2t_logits, ground_truth, criterion2)  # 计算KL损失
            loss1 = CLAS2(logits, label, seq_len, criterion)  # 计算主分类损失
            
            # 增强的MAL损失 - 边际感知对抗损失（优化权重）
            loss_mal = MultiMarginContrastive(v_feat, label, seq_len, margin=0.15, temperature=0.15)

            # 协同正则化损失 - 鼓励模块间协同
            loss_sync = 0.0
            if hasattr(model.self_attention, 'feature_amplifier') and model.self_attention.use_amplifier:
                # TCA-Amplifier协同损失
                tca_alpha = model.self_attention.self_attn.alpha
                amp_alpha = model.self_attention.feature_amplifier.alpha_enhance
                sync_loss = F.mse_loss(torch.sigmoid(tca_alpha), torch.sigmoid(amp_alpha))
                loss_sync += 0.001 * sync_loss

            # 计算总损失（优化的权重分配）
            loss = loss1 + lamda * loss2 + 0.05 * loss_mal + loss_sync  # 增加MAL损失权重

            optimizer.zero_grad()  # 梯度清零
            loss.backward()  # 反向传播
            
            # 梯度裁剪，防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0)
            
            optimizer.step()  # 优化器更新参数

            t_loss.append(loss1)  # 记录主损失
            s_loss.append(loss2)  # 记录辅助损失
            m_loss.append(loss_mal)  # 记录MAL损失

    # 返回平均主损失、辅助损失和MAL损失
    return sum(t_loss) / len(t_loss), sum(s_loss) / len(s_loss)
